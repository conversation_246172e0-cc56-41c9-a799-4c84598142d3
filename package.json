{"name": "flex-template", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "npm-run-all --parallel dev:frontend dev:backend", "dev:frontend": "vite --open", "dev:backend": "convex dev", "lint": "tsc -p convex -noEmit --pretty false && tsc -p . -noEmit --pretty false && convex dev --once && vite build"}, "dependencies": {"@ai-sdk/anthropic": "^1.2.12", "@ai-sdk/cohere": "^1.2.10", "@ai-sdk/deepseek": "^0.2.14", "@ai-sdk/google": "^1.2.19", "@ai-sdk/groq": "^1.2.9", "@ai-sdk/mistral": "^1.2.8", "@ai-sdk/openai": "^1.3.22", "@convex-dev/auth": "^0.0.80", "@openrouter/ai-sdk-provider": "^0.7.2", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@tsparticles/engine": "^3.8.1", "@tsparticles/react": "^3.0.0", "ai": "^4.3.16", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "convex": "^1.24.2", "lucide-react": "^0.514.0", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-markdown": "^10.1.0", "react-tsparticles": "^2.12.2", "remark-gfm": "^4.0.1", "shadcn-ui": "^0.9.5", "sonner": "^2.0.3", "tailwind-merge": "^3.1.0", "tailwindcss-animate": "^1.0.7", "tsparticles": "^3.8.1", "zod": "^3.25.63"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/node": "^22.13.10", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "~10", "dotenv": "^16.4.7", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "npm-run-all": "^4.1.5", "postcss": "~8", "prettier": "^3.5.3", "tailwindcss": "~3", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.2.0"}}