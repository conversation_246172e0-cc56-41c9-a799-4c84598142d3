import { useState, useRef, useEffect, forwardRef } from "react";
import { useQuery, useMutation, useAction } from "convex/react";
import { api } from "../../convex/_generated/api";
import { ArrowUp, X, Sparkles, Wrench, Search, Calculator, Clock, CloudRain, ChevronDown, Zap, Brain, Bot, Star, Plus } from "lucide-react";
import { Textarea } from "@/components/ui/textarea";
import { But<PERSON> } from "@/components/ui/button";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { DropdownMenu, DropdownMenuContent, DropdownMenuCheckboxItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { ModelSelector } from "./ModelSelector";
import React from "react";

type AiProvider = "openai" | "anthropic" | "google" | "openrouter" | "groq" | "deepseek" | "grok" | "cohere" | "mistral";

interface MessageInputProps {
  onSendMessage: (content: string, attachments?: any[], selectedModel?: { provider: string; model: string }, enabledTools?: string[]) => void;
  disabled?: boolean;
}

const ChatTextarea = forwardRef<HTMLTextAreaElement, any>((props, ref) => {
  return (
    <Textarea
      ref={ref}
      {...props}
      rows={1}
      placeholder="Message ErzenAI..."
      className="flex-1 resize-none border-0 shadow-none focus-visible:ring-0 bg-transparent py-3 px-0 text-base placeholder:text-muted-foreground/60 min-h-[40px] max-h-40"
    />
  );
});

const PROVIDER_CONFIGS = {
  openai: {
    name: "OpenAI",
    models: [
      "gpt-4o-mini-2024-07-18",
      "chatgpt-4o-latest", 
      "o3-mini",
      "o4-mini",
      "gpt-4.1",
      "gpt-4.1-mini",
      "gpt-4.1-nano",
      "gpt-4o",
      "gpt-4o-mini",
      "gpt-4-turbo",
      "gpt-3.5-turbo"
    ],
    icon: Bot,
    color: "bg-gradient-to-r from-green-500 to-emerald-600",
    textColor: "text-white",
  },
  google: {
    name: "Google AI",
    models: [
      "gemini-2.0-flash",
      "gemini-2.0-flash-lite", 
      "gemini-2.5-pro-preview-05-06",
      "gemini-2.5-flash-preview-05-20",
      "gemini-1.5-pro",
      "gemini-1.5-flash"
    ],
    icon: Sparkles,
    color: "bg-gradient-to-r from-blue-500 to-cyan-600",
    textColor: "text-white",
  },
  anthropic: {
    name: "Anthropic",
    models: [
      "claude-sonnet-4-20250514",
      "claude-opus-4-20250514",
      "claude-3-7-sonnet-latest",
      "claude-3-5-sonnet-latest",
      "claude-3-5-haiku-latest",
      "claude-3-5-sonnet-20241022",
      "claude-3-haiku-20240307",
      "claude-3-sonnet-20240229",
      "claude-3-opus-20240229"
    ],
    icon: Brain,
    color: "bg-gradient-to-r from-orange-500 to-red-600",
    textColor: "text-white",
  },
  openrouter: {
    name: "OpenRouter",
    models: [
      "deepseek/deepseek-chat-v3-0324:free",
      "deepseek/deepseek-r1:free",
      "tngtech/deepseek-r1t-chimera:free",
      "deepseek/deepseek-prover-v2:free",
      "mistralai/devstral-small:free",
      "qwen/qwen2.5-vl-72b-instruct:free",
      "mistralai/mistral-small-3.1-24b-instruct:free",
      "google/gemma-3-27b-it:free",
      "rekaai/reka-flash-3:free",
      "google/gemini-2.5-pro-exp-03-25:free",
      "qwen/qwen3-235b-a22b:free",
      "qwen/qwen3-30b-a3b:free",
      "qwen/qwen3-32b:free",
      "nvidia/llama-3.1-nemotron-ultra-253b-v1:free",
      "anthropic/claude-3.5-sonnet",
      "openai/gpt-4o",
      "google/gemini-pro-1.5",
      "meta-llama/llama-3.1-405b-instruct",
      "mistralai/mixtral-8x7b-instruct",
      "cohere/command-r-plus",
    ],
    icon: Zap,
    color: "bg-gradient-to-r from-purple-500 to-pink-600",
    textColor: "text-white",
  },
  groq: {
    name: "Groq",
    models: [
      "deepseek-r1-distill-llama-70b",
      "deepseek-r1-distill-qwen-32b",
      "llama-3.3-70b-versatile",
      "llama-3.2-90b-vision-preview",
      "llama3-70b-8192",
      "qwen-qwq-32b",
      "meta-llama/llama-4-scout-17b-16e-instruct",
      "meta-llama/llama-4-maverick-17b-128e-instruct",
      "compound-beta",
      "compound-beta-mini",
      "llama-3.1-405b-reasoning",
      "llama-3.1-70b-versatile",
      "llama-3.1-8b-instant",
      "mixtral-8x7b-32768",
      "gemma2-9b-it",
    ],
    icon: Zap,
    color: "bg-gradient-to-r from-yellow-500 to-orange-600",
    textColor: "text-white",
  },
  deepseek: {
    name: "DeepSeek",
    models: ["deepseek-chat", "deepseek-coder"],
    icon: Search,
    color: "bg-gradient-to-r from-indigo-500 to-purple-600",
    textColor: "text-white",
  },
  grok: {
    name: "Grok",
    models: ["grok-beta", "grok-vision-beta"],
    icon: Sparkles,
    color: "bg-gradient-to-r from-gray-800 to-black",
    textColor: "text-white",
  },
  cohere: {
    name: "Cohere",
    models: ["command-r-plus", "command-r", "command"],
    icon: Brain,
    color: "bg-gradient-to-r from-teal-500 to-cyan-600",
    textColor: "text-white",
  },
  mistral: {
    name: "Mistral",
    models: [
      "accounts/fireworks/models/mistral-small-24b-instruct-2501",
      "mistral-large-latest",
      "mistral-medium-latest",
      "mistral-small-latest",
      "codestral-latest",
    ],
    icon: Zap,
    color: "bg-gradient-to-r from-rose-500 to-pink-600",
    textColor: "text-white",
  },
};

const AVAILABLE_TOOLS = [
  {
    id: "web_search",
    name: "Web Search",
    description: "Search the web for current information",
    icon: Search,
    requiresApiKey: "tavily",
  },
  {
    id: "deep_search",
    name: "Deep Search",
    description: "Comprehensive research with multiple queries (3x pricing)",
    icon: Search,
    requiresApiKey: "tavily",
    premium: true,
  },
  {
    id: "weather",
    name: "Weather",
    description: "Get current weather information",
    icon: CloudRain,
    requiresApiKey: "openweather",
  },
  {
    id: "datetime",
    name: "Date & Time",
    description: "Get current date and time information",
    icon: Clock,
    requiresApiKey: null,
  },
  {
    id: "calculator",
    name: "Calculator",
    description: "Perform mathematical calculations",
    icon: Calculator,
    requiresApiKey: null,
  },
];

export function MessageInput({ onSendMessage, disabled }: MessageInputProps) {
  const [message, setMessage] = useState("");
  const [attachments, setAttachments] = useState<any[]>([]);
  const [selectedProvider, setSelectedProvider] = useState<AiProvider>("openai");
  const [selectedModel, setSelectedModel] = useState("gpt-4o-mini");
  const [enabledTools, setEnabledTools] = useState<string[]>([]);
  const [showModelSelector, setShowModelSelector] = useState(false);
  const [activeTab, setActiveTab] = useState<"favorites" | "all">("favorites");
  const [availableProviders, setAvailableProviders] = useState<string[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const modelSelectorRef = useRef<HTMLDivElement>(null);
  
  const preferences = useQuery(api.preferences.get);
  const updatePreferences = useMutation(api.preferences.update);
  const userApiKeys = useQuery(api.apiKeys.list) || [];
  const toggleFavoriteModel = useMutation(api.preferences.toggleFavoriteModel);
  const getAvailableProviders = useAction(api.ai.getAvailableProviders);

  const favoriteModels = preferences?.favoriteModels || [];

  useEffect(() => {
    if (preferences) {
      setEnabledTools(preferences.enabledTools ?? []);
      setSelectedProvider(preferences.aiProvider ?? "openai");
      setSelectedModel(preferences.model ?? "gpt-4o-mini");
    }
  }, [preferences]);

  // Load available providers
  useEffect(() => {
    const loadProviders = async () => {
      try {
        const providers = await getAvailableProviders();
        setAvailableProviders(providers);
      } catch (error) {
        console.error("Failed to load available providers:", error);
        const fallbackProviders = Object.keys(PROVIDER_CONFIGS).filter(provider => {
          if (provider === "openai" || provider === "google") return true;
          return userApiKeys.some(key => key.provider === provider && key.hasKey);
        });
        setAvailableProviders(fallbackProviders);
      }
    };
    void loadProviders();
  }, [getAvailableProviders, userApiKeys]);

  // Model selector outside click handler
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (modelSelectorRef.current && !modelSelectorRef.current.contains(event.target as Node)) {
        setShowModelSelector(false);
        setSearchQuery("");
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setMessage(e.target.value);
    adjustTextareaHeight();
  };

  const adjustTextareaHeight = () => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!message.trim() && attachments.length === 0 || disabled) return;

    onSendMessage(
      message, 
      attachments.length > 0 ? attachments : undefined,
      { provider: selectedProvider, model: selectedModel },
      enabledTools
    );
    setMessage("");
    setAttachments([]);
    if (textareaRef.current) textareaRef.current.style.height = 'auto';
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e as any);
    }
  };
  
  const handleModelSelect = (provider: string, model: string) => {
    const typedProvider = provider as AiProvider;
    setSelectedProvider(typedProvider);
    setSelectedModel(model);
    if (preferences) {
      void updatePreferences({ ...preferences, aiProvider: typedProvider, model });
    }
  };
  
  const handleToolsChange = (tools: string[]) => {
    setEnabledTools(tools);
    if (preferences) {
      void updatePreferences({ ...preferences, enabledTools: tools });
    }
  };

  /* Toggle a tool on/off, persisting the preference */
  const handleToggleTool = (toolId: string) => {
    const newTools = enabledTools.includes(toolId)
      ? enabledTools.filter((t) => t !== toolId)
      : [...enabledTools, toolId];
    handleToolsChange(newTools);
  };

  const handleModelSelectInternal = (provider: AiProvider, model: string) => {
    handleModelSelect(provider, model);
    setShowModelSelector(false);
    setSearchQuery("");
  };

  const handleToggleFavorite = async (e: React.MouseEvent, provider: string, model: string) => {
    e.stopPropagation();
    await toggleFavoriteModel({ provider, model });
  };

  const isFavorite = (provider: string, model: string) => {
    return favoriteModels.some(fav => fav.provider === provider && fav.model === model);
  };

  const getFavoriteModels = () => {
    const filtered = favoriteModels.filter(fav =>
      availableProviders.includes(fav.provider) &&
      PROVIDER_CONFIGS[fav.provider as AiProvider]?.models.includes(fav.model)
    );

    if (!searchQuery) return filtered;

    return filtered.filter(fav =>
      getDisplayName(fav.model).toLowerCase().includes(searchQuery.toLowerCase()) ||
      fav.model.toLowerCase().includes(searchQuery.toLowerCase())
    );
  };

  const getFilteredProviders = () => {
    if (!searchQuery) return availableProviders;

    return availableProviders.filter(provider => {
      const providerModels = PROVIDER_CONFIGS[provider as AiProvider].models;
      return providerModels.some(model =>
        getDisplayName(model).toLowerCase().includes(searchQuery.toLowerCase()) ||
        model.toLowerCase().includes(searchQuery.toLowerCase())
      );
    });
  };

  const getFilteredModels = (provider: string) => {
    const models = PROVIDER_CONFIGS[provider as AiProvider].models;
    if (!searchQuery) return models;

    return models.filter(model =>
      getDisplayName(model).toLowerCase().includes(searchQuery.toLowerCase()) ||
      model.toLowerCase().includes(searchQuery.toLowerCase())
    );
  };

  const getDisplayName = (model: string) => {
    // OpenAI Models
    if (model === "gpt-4o-mini-2024-07-18") return "GPT-4o Mini";
    if (model === "chatgpt-4o-latest") return "ChatGPT-4o";
    if (model === "o3-mini") return "o3 Mini";
    if (model === "o4-mini") return "o4 Mini";
    if (model === "gpt-4.1") return "GPT-4.1";
    if (model === "gpt-4.1-mini") return "GPT-4.1 Mini";
    if (model === "gpt-4.1-nano") return "GPT-4.1 Nano";
    if (model === "gpt-4o") return "GPT-4o";
    if (model === "gpt-4o-mini") return "GPT-4o Mini";
    if (model === "gpt-4-turbo") return "GPT-4 Turbo";
    if (model === "gpt-3.5-turbo") return "GPT-3.5 Turbo";

    // Google Models
    if (model === "gemini-2.0-flash") return "Gemini 2.0 Flash";
    if (model === "gemini-2.0-flash-lite") return "Gemini 2.0 Flash Lite";
    if (model === "gemini-2.5-pro-preview-05-06") return "Gemini 2.5 Pro";
    if (model === "gemini-2.5-flash-preview-05-20") return "Gemini 2.5 Flash";
    if (model === "gemini-1.5-pro") return "Gemini 1.5 Pro";
    if (model === "gemini-1.5-flash") return "Gemini 1.5 Flash";

    // Anthropic Models
    if (model === "claude-sonnet-4-20250514") return "Claude Sonnet 4";
    if (model === "claude-opus-4-20250514") return "Claude Opus 4";
    if (model === "claude-3-7-sonnet-latest") return "Claude 3.7 Sonnet";
    if (model === "claude-3-5-sonnet-latest") return "Claude 3.5 Sonnet";
    if (model === "claude-3-5-haiku-latest") return "Claude 3.5 Haiku";
    if (model === "claude-3-5-sonnet-20241022") return "Claude 3.5 Sonnet";
    if (model === "claude-3-haiku-20240307") return "Claude 3 Haiku";
    if (model === "claude-3-sonnet-20240229") return "Claude 3 Sonnet";
    if (model === "claude-3-opus-20240229") return "Claude 3 Opus";

    // OpenRouter Models
    if (model === "deepseek/deepseek-chat-v3-0324:free") return "DeepSeek Chat";
    if (model === "deepseek/deepseek-r1:free") return "DeepSeek R1";
    if (model === "tngtech/deepseek-r1t-chimera:free") return "DeepSeek R1T Chimera";
    if (model === "deepseek/deepseek-prover-v2:free") return "DeepSeek Prover";
    if (model === "mistralai/devstral-small:free") return "Devstral Small";
    if (model === "qwen/qwen2.5-vl-72b-instruct:free") return "Qwen 2.5 VL";
    if (model === "mistralai/mistral-small-3.1-24b-instruct:free") return "Mistral Small 3.1";
    if (model === "google/gemma-3-27b-it:free") return "Gemma 3";
    if (model === "rekaai/reka-flash-3:free") return "Reka Flash 3";
    if (model === "google/gemini-2.5-pro-exp-03-25:free") return "Gemini 2.5 Pro";
    if (model === "qwen/qwen3-235b-a22b:free") return "Qwen 3 Large";
    if (model === "qwen/qwen3-30b-a3b:free") return "Qwen 3 Medium";
    if (model === "qwen/qwen3-32b:free") return "Qwen 3";
    if (model === "nvidia/llama-3.1-nemotron-ultra-253b-v1:free") return "Llama 3.1 Nemotron";
    if (model === "anthropic/claude-3.5-sonnet") return "Claude 3.5 Sonnet";
    if (model === "openai/gpt-4o") return "GPT-4o";
    if (model === "google/gemini-pro-1.5") return "Gemini Pro 1.5";
    if (model === "meta-llama/llama-3.1-405b-instruct") return "Llama 3.1 Large";
    if (model === "mistralai/mixtral-8x7b-instruct") return "Mixtral 8x7B";
    if (model === "cohere/command-r-plus") return "Command R+";

    // Groq Models
    if (model === "deepseek-r1-distill-llama-70b") return "DeepSeek R1 Llama";
    if (model === "deepseek-r1-distill-qwen-32b") return "DeepSeek R1 Qwen";
    if (model === "llama-3.3-70b-versatile") return "Llama 3.3";
    if (model === "llama-3.2-90b-vision-preview") return "Llama 3.2 Vision";
    if (model === "llama3-70b-8192") return "Llama 3";
    if (model === "qwen-qwq-32b") return "Qwen QwQ";
    if (model === "meta-llama/llama-4-scout-17b-16e-instruct") return "Llama 4 Scout";
    if (model === "meta-llama/llama-4-maverick-17b-128e-instruct") return "Llama 4 Maverick";
    if (model === "compound-beta") return "Compound";
    if (model === "compound-beta-mini") return "Compound Mini";
    if (model === "llama-3.1-405b-reasoning") return "Llama 3.1 Reasoning";
    if (model === "llama-3.1-70b-versatile") return "Llama 3.1";
    if (model === "llama-3.1-8b-instant") return "Llama 3.1 Instant";
    if (model === "mixtral-8x7b-32768") return "Mixtral 8x7B";
    if (model === "gemma2-9b-it") return "Gemma 2";

    // DeepSeek Models
    if (model === "deepseek-chat") return "DeepSeek Chat";
    if (model === "deepseek-coder") return "DeepSeek Coder";

    // Grok Models
    if (model === "grok-beta") return "Grok";
    if (model === "grok-vision-beta") return "Grok Vision";

    // Cohere Models
    if (model === "command-r-plus") return "Command R+";
    if (model === "command-r") return "Command R";
    if (model === "command") return "Command";

    // Mistral Models
    if (model === "accounts/fireworks/models/mistral-small-24b-instruct-2501") return "Mistral Small";
    if (model === "mistral-large-latest") return "Mistral Large";
    if (model === "mistral-medium-latest") return "Mistral Medium";
    if (model === "mistral-small-latest") return "Mistral Small";
    if (model === "codestral-latest") return "Codestral";

    // Fallback for unknown models
    return model.length > 25 ? model.substring(0, 25) + '...' : model;
  };

  const getModelInfo = (model: string) => {
    // Return additional model information for tooltips/badges
    const info: { params?: string; context?: string; features?: string[]; free?: boolean } = {};

    // Parameter counts
    if (model.includes('405b')) info.params = '405B';
    else if (model.includes('253b')) info.params = '253B';
    else if (model.includes('235b')) info.params = '235B';
    else if (model.includes('90b')) info.params = '90B';
    else if (model.includes('72b')) info.params = '72B';
    else if (model.includes('70b')) info.params = '70B';
    else if (model.includes('32b')) info.params = '32B';
    else if (model.includes('30b')) info.params = '30B';
    else if (model.includes('27b')) info.params = '27B';
    else if (model.includes('24b')) info.params = '24B';
    else if (model.includes('17b')) info.params = '17B';
    else if (model.includes('9b')) info.params = '9B';
    else if (model.includes('8b')) info.params = '8B';

    // Context lengths
    if (model.includes('32768')) info.context = '32K';
    else if (model.includes('8192')) info.context = '8K';

    // Features
    if (model.includes('vision')) info.features = ['Vision'];
    if (model.includes('reasoning')) info.features = ['Reasoning'];
    if (model.includes('coder') || model.includes('codestral')) info.features = ['Code'];

    // Free tier
    if (model.includes(':free')) info.free = true;

    return info;
  };

  const currentProvider = PROVIDER_CONFIGS[selectedProvider];
  const IconComponent = currentProvider.icon;

  return (
    <div className="w-full max-w-4xl mx-auto px-4">
      <form onSubmit={handleSubmit} className="relative">
        <div className="flex flex-col gap-4 rounded-2xl border bg-background/95 backdrop-blur-sm shadow-xl p-4 mx-auto">
        {/* Attachment preview */}
        {attachments.length > 0 && (
          <div className="flex gap-3 px-2 pb-3 border-b border-border/50">
            {attachments.map((file, index) => (
              <div key={index} className="relative group">
                <img src={file.url} alt={file.name} className="w-16 h-16 object-cover rounded-xl"/>
                <button
                  onClick={() => setAttachments(atts => atts.filter((_, i) => i !== index))}
                  className="absolute -top-2 -right-2 bg-red-500 hover:bg-red-600 text-white rounded-full p-1 shadow-sm"
                >
                  <X size={12} />
                </button>
              </div>
            ))}
          </div>
        )}

        <div className="flex items-end gap-4">
          {/* Left side controls */}
          <div className="flex items-center gap-2">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button type="button" variant="ghost" size="sm" className="h-9 w-9 p-0 rounded-lg">
                    <Plus size={16} />
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="top">
                  <p>Attach files</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            {/* Model Selector with Favorites */}
            <div className="relative" ref={modelSelectorRef}>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => setShowModelSelector(!showModelSelector)}
                className="h-9 px-3 py-0 text-sm font-medium hover:bg-muted/50 rounded-lg"
              >
                <div className="flex items-center gap-2">
                  <IconComponent size={14} />
                  <span className="max-w-24 truncate">{getDisplayName(selectedModel)}</span>
                  <ChevronDown size={12} className={cn("transition-transform", showModelSelector && "rotate-180")} />
                </div>
              </Button>

              {showModelSelector && (
                <div className="absolute bottom-full mb-2 left-0 bg-popover border rounded-xl shadow-xl z-50 min-w-80 max-h-96 overflow-hidden">
                  {/* Search */}
                  <div className="p-3 border-b border-border/50">
                    <div className="relative">
                      <Search size={14} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" />
                      <input
                        type="text"
                        placeholder="Search models..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="w-full pl-9 pr-3 py-2 text-sm bg-background border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/20"
                      />
                    </div>
                  </div>

                  {/* Tabs */}
                  <div className="flex border-b border-border/50 text-sm">
                    <button
                      onClick={() => setActiveTab("favorites")}
                      className={cn(
                        "flex-1 px-4 py-3 font-medium transition-colors",
                        activeTab === "favorites"
                          ? "bg-primary/10 text-primary border-b-2 border-primary"
                          : "text-muted-foreground hover:text-foreground"
                      )}
                    >
                      <Star size={14} className="inline mr-2" />
                      Favorites
                    </button>
                    <button
                      onClick={() => setActiveTab("all")}
                      className={cn(
                        "flex-1 px-4 py-3 font-medium transition-colors",
                        activeTab === "all"
                          ? "bg-primary/10 text-primary border-b-2 border-primary"
                          : "text-muted-foreground hover:text-foreground"
                      )}
                    >
                      All Models
                    </button>
                  </div>

                  <div className="max-h-72 overflow-y-auto">
                    {activeTab === "favorites" ? (
                      <div className="p-2">
                        {getFavoriteModels().length > 0 ? (
                          getFavoriteModels().map(fav => {
                            const modelInfo = getModelInfo(fav.model);
                            return (
                              <TooltipProvider key={`${fav.provider}:${fav.model}`}>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <button
                                      onClick={() => handleModelSelectInternal(fav.provider as AiProvider, fav.model)}
                                      className={cn(
                                        "w-full text-left px-3 py-2.5 hover:bg-muted/50 text-sm rounded-lg flex items-center justify-between transition-colors",
                                        selectedProvider === fav.provider && selectedModel === fav.model
                                          ? 'bg-primary/10 text-primary'
                                          : 'text-foreground'
                                      )}
                                    >
                                      <div className="flex items-center gap-3">
                                        {(() => {
                                          const IconComp = PROVIDER_CONFIGS[fav.provider as AiProvider].icon;
                                          return <IconComp size={14} />;
                                        })()}
                                        <span className="font-medium">{getDisplayName(fav.model)}</span>
                                        {modelInfo.params && (
                                          <span className="text-xs text-muted-foreground bg-muted/50 px-1.5 py-0.5 rounded">
                                            {modelInfo.params}
                                          </span>
                                        )}
                                      </div>
                                      <Star size={14} className="text-yellow-500 fill-current" />
                                    </button>
                                  </TooltipTrigger>
                                  <TooltipContent side="left" className="max-w-xs">
                                    <div className="space-y-1">
                                      <div className="font-medium">{getDisplayName(fav.model)}</div>
                                      <div className="text-xs text-muted-foreground">Provider: {PROVIDER_CONFIGS[fav.provider as AiProvider].name}</div>
                                      <div className="text-xs text-muted-foreground">Model ID: {fav.model}</div>
                                      {modelInfo.params && <div className="text-xs">Parameters: {modelInfo.params}</div>}
                                      {modelInfo.context && <div className="text-xs">Context: {modelInfo.context}</div>}
                                      {modelInfo.features && <div className="text-xs">Features: {modelInfo.features.join(', ')}</div>}
                                      {modelInfo.free && <div className="text-xs text-green-600">Free tier available</div>}
                                    </div>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                            );
                          })
                        ) : (
                          <div className="p-4 text-sm text-muted-foreground text-center">
                            {searchQuery ? "No matching favorites found." : "No favorites yet. Star models below to add them here."}
                          </div>
                        )}
                      </div>
                    ) : (
                      <div className="p-2">
                        {getFilteredProviders().length > 0 ? (
                          getFilteredProviders().map(provider => {
                            const filteredModels = getFilteredModels(provider);
                            if (filteredModels.length === 0) return null;

                            return (
                              <div key={provider} className="mb-3">
                                <div className="px-3 py-2 bg-muted/30 text-sm font-medium text-muted-foreground flex items-center gap-3 rounded-lg">
                                  {(() => {
                                    const IconComp = PROVIDER_CONFIGS[provider as AiProvider].icon;
                                    return <IconComp size={14} />;
                                  })()}
                                  {PROVIDER_CONFIGS[provider as AiProvider].name}
                                  {(provider === "openai" || provider === "google") && (
                                    <Badge variant="secondary" className="text-xs px-2 py-0.5">Built-in</Badge>
                                  )}
                                </div>
                                <div className="mt-1 space-y-1">
                                  {filteredModels.map(model => {
                                    const modelInfo = getModelInfo(model);
                                    return (
                                      <TooltipProvider key={model}>
                                        <Tooltip>
                                          <TooltipTrigger asChild>
                                            <button
                                              onClick={() => handleModelSelectInternal(provider as AiProvider, model)}
                                              className={cn(
                                                "w-full text-left px-3 py-2.5 hover:bg-muted/50 text-sm rounded-lg flex items-center justify-between transition-colors",
                                                selectedProvider === provider && selectedModel === model
                                                  ? 'bg-primary/10 text-primary'
                                                  : 'text-foreground'
                                              )}
                                            >
                                              <div className="flex items-center gap-2 flex-1 min-w-0">
                                                <span className="truncate">{getDisplayName(model)}</span>
                                                {modelInfo.params && (
                                                  <span className="text-xs text-muted-foreground bg-muted/50 px-1.5 py-0.5 rounded flex-shrink-0">
                                                    {modelInfo.params}
                                                  </span>
                                                )}
                                                {modelInfo.free && (
                                                  <span className="text-xs text-green-600 bg-green-100 dark:bg-green-900/30 px-1.5 py-0.5 rounded flex-shrink-0">
                                                    Free
                                                  </span>
                                                )}
                                              </div>
                                              <button
                                                onClick={(e) => void handleToggleFavorite(e, provider, model)}
                                                className="p-1 hover:bg-muted rounded-md flex-shrink-0"
                                              >
                                                <Star
                                                  size={14}
                                                  className={isFavorite(provider, model) ? "text-yellow-500 fill-current" : "text-muted-foreground"}
                                                />
                                              </button>
                                            </button>
                                          </TooltipTrigger>
                                          <TooltipContent side="left" className="max-w-xs">
                                            <div className="space-y-1">
                                              <div className="font-medium">{getDisplayName(model)}</div>
                                              <div className="text-xs text-muted-foreground">Provider: {PROVIDER_CONFIGS[provider as AiProvider].name}</div>
                                              <div className="text-xs text-muted-foreground">Model ID: {model}</div>
                                              {modelInfo.params && <div className="text-xs">Parameters: {modelInfo.params}</div>}
                                              {modelInfo.context && <div className="text-xs">Context: {modelInfo.context}</div>}
                                              {modelInfo.features && <div className="text-xs">Features: {modelInfo.features.join(', ')}</div>}
                                              {modelInfo.free && <div className="text-xs text-green-600">Free tier available</div>}
                                            </div>
                                          </TooltipContent>
                                        </Tooltip>
                                      </TooltipProvider>
                                    );
                                  })}
                                </div>
                              </div>
                            );
                          })
                        ) : (
                          <div className="p-4 text-sm text-muted-foreground text-center">
                            No models found matching "{searchQuery}".
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Message Input */}
          <div className="flex-1 px-4">
            <ChatTextarea
              ref={textareaRef}
              value={message}
              onChange={handleInputChange}
              onKeyDown={handleKeyDown}
            />
          </div>

          {/* Right side controls */}
          <div className="flex items-center gap-2">
            {/* Tools */}
            <DropdownMenu>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <DropdownMenuTrigger asChild>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className={cn(
                          "h-9 w-9 p-0 relative rounded-lg",
                          enabledTools.length > 0 ? "text-primary" : "text-muted-foreground"
                        )}
                      >
                        <Wrench size={16} />
                        {enabledTools.length > 0 && (
                          <div className="absolute -top-1 -right-1 h-4 w-4 bg-primary rounded-full text-[10px] text-primary-foreground flex items-center justify-center">
                            {enabledTools.length}
                          </div>
                        )}
                      </Button>
                    </DropdownMenuTrigger>
                  </TooltipTrigger>
                  <TooltipContent side="top">
                    <p>Tools {enabledTools.length > 0 && `(${enabledTools.length})`}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              <DropdownMenuContent className="w-64">
                <DropdownMenuLabel className="text-sm font-medium">Tools</DropdownMenuLabel>
                <DropdownMenuSeparator />
                {AVAILABLE_TOOLS.map((tool) => {
                  const IconComponent = tool.icon;
                  return (
                    <DropdownMenuCheckboxItem
                        key={tool.id}
                        checked={enabledTools.includes(tool.id)}
                        onCheckedChange={() => handleToggleTool(tool.id)}
                        onSelect={(e) => e.preventDefault()}
                        className="text-sm py-2"
                    >
                        <IconComponent size={14} className="mr-3" />
                        {tool.name}
                        {tool.premium && (
                          <Badge variant="secondary" className="ml-auto text-xs">Pro</Badge>
                        )}
                    </DropdownMenuCheckboxItem>
                  );
                })}
              </DropdownMenuContent>
            </DropdownMenu>

            {/* Send Button */}
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    type="submit"
                    size="sm"
                    disabled={disabled || (!message.trim() && attachments.length === 0)}
                    className={cn(
                      "h-9 w-9 p-0 rounded-lg transition-all",
                      message.trim() || attachments.length > 0
                        ? "bg-primary hover:bg-primary/90 text-primary-foreground"
                        : "bg-muted text-muted-foreground cursor-not-allowed"
                    )}
                  >
                    <ArrowUp size={16} />
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="top">
                  <p>Send message</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>
      </div>
    </form>
    </div>
  );
}
