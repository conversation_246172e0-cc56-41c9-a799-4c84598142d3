import { v } from "convex/values";
import { query, mutation, action } from "./_generated/server";
import { getAuthUserId } from "@convex-dev/auth/server";
import { api } from "./_generated/api";
import { paginationOptsValidator } from "convex/server";

export const list = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    return await ctx.db
      .query("conversations")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .order("desc")
      .collect();
  },
});

export const get = query({
  args: { conversationId: v.id("conversations") },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const conversation = await ctx.db.get(args.conversationId);
    if (!conversation || conversation.userId !== userId) {
      throw new Error("Conversation not found");
    }

    return conversation;
  },
});

export const create = mutation({
  args: { title: v.string() },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    return await ctx.db.insert("conversations", {
      userId,
      title: args.title,
      lastMessageAt: Date.now(),
    });
  },
});

export const updateTitle = mutation({
  args: {
    conversationId: v.id("conversations"),
    title: v.string(),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const conversation = await ctx.db.get(args.conversationId);
    if (!conversation || conversation.userId !== userId) {
      throw new Error("Conversation not found");
    }

    await ctx.db.patch(args.conversationId, {
      title: args.title,
    });
  },
});

export const remove = mutation({
  args: { conversationId: v.id("conversations") },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const conversation = await ctx.db.get(args.conversationId);
    if (!conversation || conversation.userId !== userId) {
      throw new Error("Conversation not found");
    }

    // Delete all messages in the conversation
    const messages = await ctx.db
      .query("messages")
      .withIndex("by_conversation", (q) =>
        q.eq("conversationId", args.conversationId)
      )
      .collect();

    for (const message of messages) {
      await ctx.db.delete(message._id);
    }

    // Delete the conversation
    await ctx.db.delete(args.conversationId);
  },
});

export const duplicate = mutation({
  args: { conversationId: v.id("conversations") },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const originalConversation = await ctx.db.get(args.conversationId);
    if (!originalConversation || originalConversation.userId !== userId) {
      throw new Error("Conversation not found");
    }

    const newConversationId = await ctx.db.insert("conversations", {
      userId,
      title: `Copy of ${originalConversation.title}`,
      lastMessageAt: originalConversation.lastMessageAt,
    });

    const messages = await ctx.db
      .query("messages")
      .withIndex("by_conversation", (q) =>
        q.eq("conversationId", args.conversationId)
      )
      .order("asc")
      .collect();

    for (const message of messages) {
      await ctx.db.insert("messages", {
        conversationId: newConversationId,
        role: message.role,
        content: message.content,
        attachments: message.attachments,
        toolCalls: message.toolCalls,
      });
    }

    return newConversationId;
  },
});

export const listWithMessageCounts = query({
  args: { paginationOpts: paginationOptsValidator },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const result = await ctx.db
      .query("conversations")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .order("desc")
      .paginate(args.paginationOpts);

    // Get message counts for each conversation and filter those with > 1 message
    const conversationsWithCounts = await Promise.all(
      result.page.map(async (conversation) => {
        const messageCount = await ctx.db
          .query("messages")
          .withIndex("by_conversation", (q) =>
            q.eq("conversationId", conversation._id)
          )
          .collect()
          .then((messages) => messages.length);

        return {
          ...conversation,
          messageCount,
        };
      })
    );

    // Only return conversations with more than 1 message
    const filteredConversations = conversationsWithCounts.filter(
      (conv) => conv.messageCount > 1
    );

    return {
      ...result,
      page: filteredConversations,
    };
  },
});

export const generateTitle = action({
  args: {
    conversationId: v.id("conversations"),
    firstUserMessage: v.optional(v.string()),
  },
  returns: v.string(),
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const conversation = await ctx.runQuery(api.conversations.get, {
      conversationId: args.conversationId,
    });
    if (!conversation || conversation.userId !== userId) {
      throw new Error("Conversation not found");
    }

    // Build context for title generation.
    // If caller supplied a firstUserMessage we use it, otherwise we synthesise
    // a context string from up to the first message plus the most recent few messages.

    let contextText = "";

    if (args.firstUserMessage) {
      contextText = args.firstUserMessage;
    } else {
      // Fetch messages via query since actions cannot access the database directly.
      const messagesResult = await ctx.runQuery(api.messages.list, {
        conversationId: args.conversationId,
        paginationOpts: { numItems: 100, cursor: null },
      });

      const allMessages = messagesResult.page;

      if (allMessages.length > 0) {
        const firstMsg: string = allMessages[0].content;
        const recentMsgs: string[] = allMessages
          .slice(-5)
          .map((m: any) => m.content);
        contextText = [firstMsg, ...recentMsgs].join(" \n\n");
      }
    }

    // Ensure we don't send an excessively long prompt.
    const truncatedContext = contextText.split(" ").slice(0, 500).join(" ");

    // Generate title using AI
    try {
      const titleResponse: string = await ctx.runAction(api.ai.generateTitle, {
        userMessage: truncatedContext,
      });

      // Update the conversation title
      await ctx.runMutation(api.conversations.updateTitle, {
        conversationId: args.conversationId,
        title: titleResponse || "New Chat",
      });

      return titleResponse || "New Chat";
    } catch (error) {
      console.error("Failed to generate AI title:", error);

      // Fallback to simple title generation
      let fallbackTitle = truncatedContext
        .slice(0, 40)
        .split(" ")
        .slice(0, 6)
        .join(" ");

      if (truncatedContext.length > 40) {
        fallbackTitle += "...";
      }

      await ctx.runMutation(api.conversations.updateTitle, {
        conversationId: args.conversationId,
        title: fallbackTitle || "New Chat",
      });

      return fallbackTitle || "New Chat";
    }
  },
});
