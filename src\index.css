@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 1rem;
  }

  .dark {
    --background: 224 71% 4%;
    --foreground: 213 31% 91%;

    --card: 224 71% 4%;
    --card-foreground: 213 31% 91%;

    --popover: 224 71% 4%;
    --popover-foreground: 213 31% 91%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 215 28% 17%;
    --secondary-foreground: 210 40% 98%;

    --muted: 215 28% 17%;
    --muted-foreground: 217.9 10.6% 64.9%;

    --accent: 215 28% 17%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 215 28% 17%;
    --input: 215 28% 17%;
    --ring: 216 34% 17%;
  }

  .theme-blue {
    --primary: 217 91% 60%;
    --primary-foreground: 0 0% 100%;
    --ring: 217 91% 60%;
  }

  .dark.theme-blue {
    --primary: 217 91% 60%;
    --primary-foreground: 222.2 84% 4.9%;
    --ring: 217 91% 60%;
  }

  .theme-green {
    --primary: 142 71% 45%;
    --primary-foreground: 0 0% 100%;
    --ring: 142 71% 45%;
  }

  .dark.theme-green {
    --primary: 142 71% 45%;
    --primary-foreground: 0 0% 100%;
    --ring: 142 71% 45%;
  }

  .theme-purple {
    --primary: 263 85% 70%;
    --primary-foreground: 0 0% 100%;
    --ring: 263 85% 70%;
  }

  .dark.theme-purple {
    --primary: 263 85% 70%;
    --primary-foreground: 0 0% 100%;
    --ring: 263 85% 70%;
  }

  .theme-orange {
    --primary: 25 95% 53%;
    --primary-foreground: 0 0% 100%;
    --ring: 25 95% 53%;
  }

  .dark.theme-orange {
    --primary: 25 95% 53%;
    --primary-foreground: 0 0% 100%;
    --ring: 25 95% 53%;
  }

  .theme-pink {
    --primary: 330 81% 60%;
    --primary-foreground: 0 0% 100%;
    --ring: 330 81% 60%;
  }

  .dark.theme-pink {
    --primary: 330 81% 60%;
    --primary-foreground: 0 0% 100%;
    --ring: 330 81% 60%;
  }

  .theme-teal {
    --primary: 173 80% 40%;
    --primary-foreground: 0 0% 100%;
    --ring: 173 80% 40%;
  }

  .dark.theme-teal {
    --primary: 173 80% 40%;
    --primary-foreground: 0 0% 100%;
    --ring: 173 80% 40%;
  }

  .theme-red {
    --primary: 0 84% 60%;
    --primary-foreground: 0 0% 100%;
    --ring: 0 84% 60%;
  }

  .dark.theme-red {
    --primary: 0 84% 60%;
    --primary-foreground: 0 0% 100%;
    --ring: 0 84% 60%;
  }

  .theme-indigo {
    --primary: 231 48% 48%;
    --primary-foreground: 0 0% 100%;
    --ring: 231 48% 48%;
  }

  .dark.theme-indigo {
    --primary: 231 48% 48%;
    --primary-foreground: 0 0% 100%;
    --ring: 231 48% 48%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

.accent-text {
  @apply text-slate-600;
}

body {
  font-family:
    "Inter Variable",
    ui-sans-serif,
    system-ui,
    -apple-system,
    BlinkMacSystemFont,
    "Segoe UI",
    Roboto,
    "Helvetica Neue",
    Arial,
    "Noto Sans",
    sans-serif,
    "Apple Color Emoji",
    "Segoe UI Emoji",
    "Segoe UI Symbol",
    "Noto Color Emoji";
}

/* only use this to update the style of the auth input fields. use a different class for all other input fields */
.auth-input-field {
  @apply w-full px-4 py-3 rounded-lg bg-white border border-gray-200 focus:border-primary focus:ring-1 focus:ring-primary outline-none transition-shadow shadow-sm hover:shadow;
}

/* only use this to update the style of the auth buttons. use the button class for all other buttons */
.auth-button {
  @apply w-full px-4 py-3 rounded bg-primary text-white font-semibold hover:bg-primary/90 transition-colors shadow-sm hover:shadow disabled:opacity-50 disabled:cursor-not-allowed;
}
