import { useState, useEffect } from "react";
import { useMutation, useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Eye, EyeOff, Key, Trash2, Zap, Infinity as InfinityIcon, Settings, User, UserCog, FileText, Brain, Edit, Camera, Lock } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";

interface UserPreferences {
  aiProvider: "openai" | "anthropic" | "google" | "openrouter" | "groq" | "deepseek" | "grok" | "cohere" | "mistral";
  model: string;
  temperature: number;
  maxTokens: number;
  enabledTools?: string[];
  favoriteModels?: Array<{
    provider: string;
    model: string;
  }>;
  hideUserInfo?: boolean;
}

interface SettingsModalProps {
  onClose: () => void;
  preferences: UserPreferences | null;
}

const PROVIDER_CONFIGS = {
  openai: {
    name: "OpenAI",
    models: [
      "gpt-4o-mini-2024-07-18",
      "chatgpt-4o-latest", 
      "o3-mini",
      "o4-mini",
      "gpt-4.1",
      "gpt-4.1-mini",
      "gpt-4.1-nano",
      "gpt-4o",
      "gpt-4o-mini",
      "gpt-4-turbo",
      "gpt-3.5-turbo"
    ],
    keyPlaceholder: "sk-...",
    description: "GPT models from OpenAI",
    hasBuiltIn: true,
  },
  google: {
    name: "Google AI",
    models: [
      "gemini-2.0-flash",
      "gemini-2.0-flash-lite", 
      "gemini-2.5-pro-preview-05-06",
      "gemini-2.5-flash-preview-05-20",
      "gemini-1.5-pro",
      "gemini-1.5-flash"
    ],
    keyPlaceholder: "AIza...",
    description: "Gemini models from Google",
    hasBuiltIn: true,
  },
  anthropic: {
    name: "Anthropic",
    models: [
      "claude-sonnet-4-20250514",
      "claude-opus-4-20250514",
      "claude-3-7-sonnet-latest",
      "claude-3-5-sonnet-latest",
      "claude-3-5-haiku-latest",
      "claude-3-5-sonnet-20241022",
      "claude-3-haiku-20240307",
      "claude-3-sonnet-20240229",
      "claude-3-opus-20240229"
    ],
    keyPlaceholder: "sk-ant-...",
    description: "Claude models from Anthropic",
    hasBuiltIn: true,
  },
  openrouter: {
    name: "OpenRouter",
    models: [
      "deepseek/deepseek-chat-v3-0324:free",
      "deepseek/deepseek-r1:free",
      "tngtech/deepseek-r1t-chimera:free",
      "deepseek/deepseek-prover-v2:free",
      "mistralai/devstral-small:free",
      "qwen/qwen2.5-vl-72b-instruct:free",
      "mistralai/mistral-small-3.1-24b-instruct:free",
      "google/gemma-3-27b-it:free",
      "rekaai/reka-flash-3:free",
      "google/gemini-2.5-pro-exp-03-25:free",
      "qwen/qwen3-235b-a22b:free",
      "qwen/qwen3-30b-a3b:free",
      "qwen/qwen3-32b:free",
      "nvidia/llama-3.1-nemotron-ultra-253b-v1:free",
      "anthropic/claude-3.5-sonnet",
      "openai/gpt-4o",
      "google/gemini-pro-1.5",
      "meta-llama/llama-3.1-405b-instruct",
      "mistralai/mixtral-8x7b-instruct",
      "cohere/command-r-plus",
    ],
    keyPlaceholder: "sk-or-...",
    description: "Access to multiple AI models",
    hasBuiltIn: true,
  },
  groq: {
    name: "Groq",
    models: [
      "deepseek-r1-distill-llama-70b",
      "deepseek-r1-distill-qwen-32b",
      "llama-3.3-70b-versatile",
      "llama-3.2-90b-vision-preview",
      "llama3-70b-8192",
      "qwen-qwq-32b",
      "meta-llama/llama-4-scout-17b-16e-instruct",
      "meta-llama/llama-4-maverick-17b-128e-instruct",
      "compound-beta",
      "compound-beta-mini",
      "llama-3.1-405b-reasoning",
      "llama-3.1-70b-versatile",
      "llama-3.1-8b-instant",
      "mixtral-8x7b-32768",
      "gemma2-9b-it",
    ],
    keyPlaceholder: "gsk_...",
    description: "Ultra-fast inference",
    hasBuiltIn: true,
  },
  deepseek: {
    name: "DeepSeek",
    models: ["deepseek-chat", "deepseek-coder"],
    keyPlaceholder: "sk-...",
    description: "Reasoning and coding models",
    hasBuiltIn: true,
  },
  grok: {
    name: "Grok (xAI)",
    models: ["grok-beta", "grok-vision-beta"],
    keyPlaceholder: "xai-...",
    description: "Elon's AI with real-time data",
    hasBuiltIn: true,
  },
  cohere: {
    name: "Cohere",
    models: ["command-r-plus", "command-r", "command"],
    keyPlaceholder: "co_...",
    description: "Enterprise-grade language models",
    hasBuiltIn: true,
  },
  mistral: {
    name: "Mistral AI",
    models: [
      "accounts/fireworks/models/mistral-small-24b-instruct-2501",
      "mistral-large-latest",
      "mistral-medium-latest",
      "mistral-small-latest", 
      "codestral-latest",
    ],
    keyPlaceholder: "...",
    description: "European AI models",
    hasBuiltIn: true,
  },
  tavily: {
    name: "Tavily Search",
    models: [],
    keyPlaceholder: "tvly-...",
    description: "Real-time web search API",
    hasBuiltIn: true,
  },
  openweather: {
    name: "OpenWeatherMap",
    models: [],
    keyPlaceholder: "...",
    description: "Weather data API",
    hasBuiltIn: true,
  },
};

export function SettingsModal({ onClose, preferences }: SettingsModalProps) {
  const [activeTab, setActiveTab] = useState<"preferences" | "privacy" | "apikeys" | "usage" | "account" | "instructions" | "memory">("preferences");
  const [settings, setSettings] = useState<UserPreferences>({
    aiProvider: "openai",
    model: "gpt-4o-mini",
    temperature: 0.7,
    maxTokens: 1000,
    enabledTools: ["web_search", "calculator", "datetime"],
    favoriteModels: [],
    hideUserInfo: false,
  });
  const [apiKeys, setApiKeys] = useState<Record<string, string>>({});
  const [showKeys, setShowKeys] = useState<Record<string, boolean>>({});
  const [userInstructions, setUserInstructions] = useState("");
  const [editingMemory, setEditingMemory] = useState<string | null>(null);
  const [editMemoryText, setEditMemoryText] = useState("");
  const [profileName, setProfileName] = useState("");
  const [profilePicture, setProfilePicture] = useState("");
  const [showDeleteConversationsDialog, setShowDeleteConversationsDialog] = useState(false);
  const [showDeleteMemoriesDialog, setShowDeleteMemoriesDialog] = useState(false);

  const updatePreferences = useMutation(api.preferences.update);
  const upsertApiKey = useMutation(api.apiKeys.upsert);
  const removeApiKey = useMutation(api.apiKeys.remove);
  const userApiKeys = useQuery(api.apiKeys.list) || [];
  const usage = useQuery(api.usage.get);
  const limits = useQuery(api.usage.getLimits);

  // New mutations and queries
  const instructions = useQuery(api.userInstructions.get);
  const updateInstructions = useMutation(api.userInstructions.update);
  const memories = useQuery(api.userMemories.list) || [];
  const updateMemory = useMutation(api.userMemories.update);
  const removeMemory = useMutation(api.userMemories.remove);
  const removeAllMemories = useMutation(api.userMemories.removeAll);
  const updateProfile = useMutation(api.userAccount.updateProfile);
  const deleteAllConversations = useMutation(api.userAccount.deleteAllConversations);
  const user = useQuery(api.auth.loggedInUser);

  useEffect(() => {
    if (preferences) {
      // Only extract the valid preference fields, excluding Convex-generated fields
      setSettings({
        aiProvider: preferences.aiProvider,
        model: preferences.model,
        temperature: preferences.temperature,
        maxTokens: preferences.maxTokens,
        enabledTools: preferences.enabledTools,
        favoriteModels: preferences.favoriteModels,
        hideUserInfo: preferences.hideUserInfo ?? false,
      });
    }
  }, [preferences]);

  useEffect(() => {
    if (instructions) {
      setUserInstructions(instructions);
    }
  }, [instructions]);

  useEffect(() => {
    if (user) {
      setProfileName(user.name || "");
      setProfilePicture(user.image || "");
    }
  }, [user]);

  const handleSavePreferences = async () => {
    await updatePreferences(settings);
  };

  const handleSaveApiKey = async (provider: keyof typeof PROVIDER_CONFIGS) => {
    const key = apiKeys[provider];
    if (key?.trim()) {
      await upsertApiKey({ provider, apiKey: key.trim() });
      setApiKeys(prev => ({ ...prev, [provider]: "" }));
    }
  };

  const handleRemoveApiKey = async (provider: keyof typeof PROVIDER_CONFIGS) => {
    await removeApiKey({ provider });
  };

  const toggleShowKey = (provider: string) => {
    setShowKeys(prev => ({ ...prev, [provider]: !prev[provider] }));
  };

  const hasApiKey = (provider: string) => {
    return userApiKeys.some(key => key.provider === provider && key.hasKey);
  };

  const getUsagePercentage = (used: number, limit: number) => {
    return Math.min((used / limit) * 100, 100);
  };

  const getUsageColor = (percentage: number) => {
    if (percentage >= 90) return "bg-red-500";
    if (percentage >= 70) return "bg-yellow-500";
    return "bg-green-500";
  };

  const handleSaveInstructions = async () => {
    await updateInstructions({ instructions: userInstructions });
  };

  const handleSaveProfile = async () => {
    await updateProfile({
      name: profileName || undefined,
      profilePicture: profilePicture || undefined
    });
  };

  const handleDeleteAllConversations = async () => {
    await deleteAllConversations();
    setShowDeleteConversationsDialog(false);
  };

  const handleDeleteAllMemories = async () => {
    await removeAllMemories();
    setShowDeleteMemoriesDialog(false);
  };

  const handleEditMemory = (memoryId: string, currentText: string) => {
    setEditingMemory(memoryId);
    setEditMemoryText(currentText);
  };

  const handleSaveMemory = async () => {
    if (editingMemory) {
      await updateMemory({ id: editingMemory as any, memory: editMemoryText });
      setEditingMemory(null);
      setEditMemoryText("");
    }
  };

  const handleDeleteMemory = async (memoryId: string) => {
    await removeMemory({ id: memoryId as any });
  };

  const getWordCount = (text: string) => {
    return text.trim().split(/\s+/).filter(word => word.length > 0).length;
  };

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden p-0">
        <DialogHeader className="px-6 py-4 border-b">
          <DialogTitle className="text-xl font-semibold">Settings</DialogTitle>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as "preferences" | "privacy" | "apikeys" | "usage" | "account" | "instructions" | "memory")} className="flex-1 flex flex-col">
          <TabsList className="grid w-full grid-cols-7 mx-6 mt-4">
            <TabsTrigger value="preferences" className="flex items-center gap-1 text-xs">
              <Settings size={14} />
              AI
            </TabsTrigger>
            <TabsTrigger value="privacy" className="flex items-center gap-1 text-xs">
              <User size={14} />
              Privacy
            </TabsTrigger>
            <TabsTrigger value="apikeys" className="flex items-center gap-1 text-xs">
              <Key size={14} />
              API Keys
            </TabsTrigger>
            <TabsTrigger value="usage" className="flex items-center gap-1 text-xs">
              <Zap size={14} />
              Usage
            </TabsTrigger>
            <TabsTrigger value="account" className="flex items-center gap-1 text-xs">
              <UserCog size={14} />
              Account
            </TabsTrigger>
            <TabsTrigger value="instructions" className="flex items-center gap-1 text-xs">
              <FileText size={14} />
              Instructions
            </TabsTrigger>
            <TabsTrigger value="memory" className="flex items-center gap-1 text-xs">
              <Brain size={14} />
              Memory
            </TabsTrigger>
          </TabsList>

          <div className="overflow-y-auto max-h-[60vh] px-6 py-4">
            <TabsContent value="preferences" className="space-y-6 mt-0">
              <Card>
                <CardHeader>
                  <CardTitle>AI Model Configuration</CardTitle>
                  <CardDescription>
                    Configure your preferred AI provider and model settings
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* AI Provider */}
                  <div className="space-y-2">
                    <Label htmlFor="provider">AI Provider</Label>
                    <Select
                      value={settings.aiProvider}
                      onValueChange={(value) => setSettings(prev => ({
                        ...prev,
                        aiProvider: value as UserPreferences["aiProvider"],
                        model: PROVIDER_CONFIGS[value as keyof typeof PROVIDER_CONFIGS].models[0] ?? "",
                      }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select provider" />
                      </SelectTrigger>
                      <SelectContent>
                        {Object.entries(PROVIDER_CONFIGS)
                          .filter(([key]) => !["tavily", "openweather"].includes(key))
                          .map(([key, config]) => (
                            <SelectItem key={key} value={key}>
                              {config.name} - {config.description}
                            </SelectItem>
                          ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Model */}
                  {PROVIDER_CONFIGS[settings.aiProvider].models.length > 0 && (
                    <div className="space-y-2">
                      <Label htmlFor="model">Model</Label>
                      <Select
                        value={settings.model}
                        onValueChange={(value) => setSettings(prev => ({ ...prev, model: value }))}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select model" />
                        </SelectTrigger>
                        <SelectContent>
                          {PROVIDER_CONFIGS[settings.aiProvider].models.map(model => (
                            <SelectItem key={model} value={model}>{model}</SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  )}

                  {/* Temperature */}
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <Label>Temperature</Label>
                      <Badge variant="secondary">{settings.temperature}</Badge>
                    </div>
                    <Slider
                      value={[settings.temperature]}
                      onValueChange={([value]) => setSettings(prev => ({ ...prev, temperature: value }))}
                      max={2}
                      min={0}
                      step={0.1}
                      className="w-full"
                    />
                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>Focused</span>
                      <span>Creative</span>
                    </div>
                  </div>

                  {/* Max Tokens */}
                  <div className="space-y-2">
                    <Label htmlFor="maxTokens">Max Tokens</Label>
                    <Input
                      id="maxTokens"
                      type="number"
                      min="100"
                      max="4000"
                      step="100"
                      value={settings.maxTokens}
                      onChange={(e) => setSettings(prev => ({ ...prev, maxTokens: parseInt(e.target.value) }))}
                    />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Privacy Tab */}
            <TabsContent value="privacy" className="space-y-6 mt-0">
              <Card>
                <CardHeader>
                  <CardTitle>Privacy Settings</CardTitle>
                  <CardDescription>
                    Control how your personal information is displayed
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="hideUserInfo">Hide User Information</Label>
                      <p className="text-sm text-muted-foreground">
                        Blur your name and email in the sidebar for privacy
                      </p>
                    </div>
                    <Switch
                      id="hideUserInfo"
                      checked={settings.hideUserInfo}
                      onCheckedChange={(checked) => setSettings(prev => ({ ...prev, hideUserInfo: checked }))}
                    />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* API Keys Tab */}
            <TabsContent value="apikeys" className="space-y-6 mt-0">
              <Card>
                <CardHeader>
                  <CardTitle>API Keys</CardTitle>
                  <CardDescription>
                    Add your own API keys for unlimited usage without counting against limits
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="bg-gradient-to-r from-blue-50 to-green-50 border border-blue-200 rounded-lg p-4 mb-6">
                    <div className="flex items-start gap-3">
                      <Zap className="text-blue-600 mt-1" size={20} />
                      <div>
                        <h3 className="font-medium text-blue-900 mb-1">Built-in API Keys vs Your Own</h3>
                        <div className="text-sm text-blue-800 space-y-1">
                          <p><strong>Built-in Keys:</strong> Free usage with monthly limits (messages & searches count)</p>
                          <p><strong>Your Keys:</strong> <InfinityIcon className="inline w-4 h-4" /> Unlimited usage - no limits or counting!</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    {Object.entries(PROVIDER_CONFIGS).map(([provider, config]) => (
                      <Card key={provider}>
                        <CardContent className="p-4">
                          <div className="flex items-center justify-between mb-3">
                            <div>
                              <h3 className="font-medium">{config.name}</h3>
                              <p className="text-sm text-muted-foreground">{config.description}</p>
                            </div>
                            <div className="flex items-center gap-2">
                              {hasApiKey(provider) && (
                                <Badge variant="secondary" className="bg-green-100 text-green-800">
                                  <InfinityIcon size={12} className="mr-1" />
                                  Unlimited
                                </Badge>
                              )}
                              {config.hasBuiltIn && (
                                <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                                  <Zap size={12} className="mr-1" />
                                  Built-in
                                </Badge>
                              )}
                            </div>
                          </div>

                          <div className="flex gap-2">
                            <div className="flex-1 relative">
                              <Input
                                type={showKeys[provider] ? "text" : "password"}
                                value={apiKeys[provider] || ""}
                                onChange={(e) => setApiKeys(prev => ({ ...prev, [provider]: e.target.value }))}
                                placeholder={config.keyPlaceholder}
                                className="pr-10"
                              />
                              <Button
                                type="button"
                                variant="ghost"
                                size="icon"
                                onClick={() => toggleShowKey(provider)}
                                className="absolute right-1 top-1 h-8 w-8"
                              >
                                {showKeys[provider] ? <EyeOff size={16} /> : <Eye size={16} />}
                              </Button>
                            </div>
                            <Button
                              onClick={() => void handleSaveApiKey(provider as keyof typeof PROVIDER_CONFIGS)}
                              disabled={!apiKeys[provider]?.trim()}
                              className="flex items-center gap-2"
                            >
                              <Key size={16} />
                              Save
                            </Button>
                            {hasApiKey(provider) && (
                              <Button
                                variant="destructive"
                                onClick={() => void handleRemoveApiKey(provider as keyof typeof PROVIDER_CONFIGS)}
                                className="flex items-center gap-2"
                              >
                                <Trash2 size={16} />
                              </Button>
                            )}
                          </div>

                          {!hasApiKey(provider) && config.hasBuiltIn && (
                            <p className="text-xs text-muted-foreground mt-2">
                              Using built-in key with usage limits. Add your own key for unlimited access.
                            </p>
                          )}
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Account Tab */}
            <TabsContent value="account" className="space-y-6 mt-0">
              <Card>
                <CardHeader>
                  <CardTitle>Profile Settings</CardTitle>
                  <CardDescription>
                    Manage your profile information
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-2">
                    <Label htmlFor="profileName">Display Name</Label>
                    <Input
                      id="profileName"
                      value={profileName}
                      onChange={(e) => setProfileName(e.target.value)}
                      placeholder="Enter your display name"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="profilePicture">Profile Picture URL</Label>
                    <div className="flex gap-2">
                      <Input
                        id="profilePicture"
                        value={profilePicture}
                        onChange={(e) => setProfilePicture(e.target.value)}
                        placeholder="Enter profile picture URL"
                        className="flex-1"
                      />
                      <Button
                        onClick={handleSaveProfile}
                        className="flex items-center gap-2"
                      >
                        <Camera size={16} />
                        Save
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Account Actions</CardTitle>
                  <CardDescription>
                    Manage your account data and security
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between p-4 border rounded-lg">
                    <div>
                      <h4 className="font-medium">Change Password</h4>
                      <p className="text-sm text-muted-foreground">Update your account password</p>
                    </div>
                    <Button variant="outline" className="flex items-center gap-2">
                      <Lock size={16} />
                      Change Password
                    </Button>
                  </div>

                  <div className="flex items-center justify-between p-4 border rounded-lg">
                    <div>
                      <h4 className="font-medium">Delete All Conversations</h4>
                      <p className="text-sm text-muted-foreground">Permanently delete all your conversations</p>
                    </div>
                    <Button
                      variant="destructive"
                      className="flex items-center gap-2"
                      onClick={() => setShowDeleteConversationsDialog(true)}
                    >
                      <Trash2 size={16} />
                      Delete All
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Instructions Tab */}
            <TabsContent value="instructions" className="space-y-6 mt-0">
              <Card>
                <CardHeader>
                  <CardTitle>AI Instructions</CardTitle>
                  <CardDescription>
                    Add custom instructions that will be sent to the AI with every conversation (max 500 words)
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <Label htmlFor="instructions">Instructions</Label>
                      <span className="text-sm text-muted-foreground">
                        {getWordCount(userInstructions)}/500 words
                      </span>
                    </div>
                    <Textarea
                      id="instructions"
                      value={userInstructions}
                      onChange={(e) => setUserInstructions(e.target.value)}
                      placeholder="Enter your custom instructions for the AI..."
                      className="min-h-[200px]"
                    />
                    {getWordCount(userInstructions) > 500 && (
                      <p className="text-sm text-red-500">
                        Instructions exceed 500 words. Please reduce the length.
                      </p>
                    )}
                  </div>

                  <Button
                    onClick={handleSaveInstructions}
                    disabled={getWordCount(userInstructions) > 500}
                    className="w-full"
                  >
                    Save Instructions
                  </Button>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Memory Tab */}
            <TabsContent value="memory" className="space-y-6 mt-0">
              <Card>
                <CardHeader>
                  <CardTitle>AI Memory</CardTitle>
                  <CardDescription>
                    View and manage things the AI has remembered about you
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex justify-between items-center mb-4">
                    <span className="text-sm text-muted-foreground">
                      {memories.length} memories stored
                    </span>
                    {memories.length > 0 && (
                      <Button
                        variant="destructive"
                        size="sm"
                        className="flex items-center gap-2"
                        onClick={() => setShowDeleteMemoriesDialog(true)}
                      >
                        <Trash2 size={14} />
                        Delete All
                      </Button>
                    )}
                  </div>

                  <div className="space-y-3 max-h-[400px] overflow-y-auto">
                    {memories.length === 0 ? (
                      <div className="text-center py-8 text-muted-foreground">
                        <Brain size={48} className="mx-auto mb-2 opacity-50" />
                        <p>No memories stored yet</p>
                        <p className="text-sm">The AI will remember important information as you chat</p>
                      </div>
                    ) : (
                      memories.map((memory) => (
                        <div key={memory._id} className="border rounded-lg p-3">
                          {editingMemory === memory._id ? (
                            <div className="space-y-2">
                              <Textarea
                                value={editMemoryText}
                                onChange={(e) => setEditMemoryText(e.target.value)}
                                className="min-h-[60px]"
                              />
                              <div className="flex gap-2">
                                <Button size="sm" onClick={handleSaveMemory}>
                                  Save
                                </Button>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => setEditingMemory(null)}
                                >
                                  Cancel
                                </Button>
                              </div>
                            </div>
                          ) : (
                            <div className="flex justify-between items-start gap-2">
                              <div className="flex-1">
                                <p className="text-sm">{memory.memory}</p>
                                <p className="text-xs text-muted-foreground mt-1">
                                  {new Date(memory.createdAt).toLocaleDateString()}
                                </p>
                              </div>
                              <div className="flex gap-1">
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  onClick={() => handleEditMemory(memory._id, memory.memory)}
                                >
                                  <Edit size={14} />
                                </Button>
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  onClick={() => handleDeleteMemory(memory._id)}
                                >
                                  <Trash2 size={14} />
                                </Button>
                              </div>
                            </div>
                          )}
                        </div>
                      ))
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Usage Tab */}
            <TabsContent value="usage" className="space-y-6 mt-0">
              <Card>
                <CardHeader>
                  <CardTitle>Current Plan</CardTitle>
                  <CardDescription>
                    Your usage statistics and plan information
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-6">
                    <h3 className="text-lg font-semibold mb-2">Current Plan</h3>
                    <div className="flex items-center gap-3">
                      <Badge className="capitalize">
                        {usage?.plan ?? "Free"}
                      </Badge>
                      <span className="text-muted-foreground">
                        Resets on {usage?.resetDate ? new Date(usage.resetDate).toLocaleDateString() : "N/A"}
                      </span>
                    </div>
                    <p className="text-sm text-blue-800 mt-2">
                      💡 Add your own API keys for unlimited usage without counting against these limits!
                    </p>
                  </div>
                </CardContent>
              </Card>

              {/* Usage Stats */}
              {usage && limits && (
                <Card>
                  <CardHeader>
                    <CardTitle>Usage Statistics</CardTitle>
                    <CardDescription>Built-in keys only - your own keys have unlimited usage</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {/* Messages Usage */}
                      <div className="space-y-3">
                        <h4 className="font-medium">Messages</h4>
                        <div className="space-y-2">
                          <div className="flex justify-between text-sm">
                            <span>Used</span>
                            <span>{usage.messagesUsed} / {limits.messages}</span>
                          </div>
                          <div className="w-full bg-muted rounded-full h-2">
                            <div
                              className={`h-2 rounded-full transition-all ${getUsageColor(getUsagePercentage(usage.messagesUsed, limits.messages))}`}
                              style={{ width: `${getUsagePercentage(usage.messagesUsed, limits.messages)}%` }}
                            />
                          </div>
                          <p className="text-xs text-muted-foreground">Your own API keys = unlimited messages</p>
                        </div>
                      </div>

                      {/* Searches Usage */}
                      <div className="space-y-3">
                        <h4 className="font-medium">Web Searches</h4>
                        <div className="space-y-2">
                          <div className="flex justify-between text-sm">
                            <span>Used</span>
                            <span>{usage.searchesUsed} / {limits.searches}</span>
                          </div>
                          <div className="w-full bg-muted rounded-full h-2">
                            <div
                              className={`h-2 rounded-full transition-all ${getUsageColor(getUsagePercentage(usage.searchesUsed, limits.searches))}`}
                              style={{ width: `${getUsagePercentage(usage.searchesUsed, limits.searches)}%` }}
                            />
                          </div>
                          <p className="text-xs text-muted-foreground">Your own API keys = unlimited searches</p>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

            </TabsContent>
          </div>
        </Tabs>

        <div className="flex gap-3 p-6 border-t">
          <Button variant="outline" onClick={onClose} className="flex-1">
            Cancel
          </Button>
          {(activeTab === "preferences" || activeTab === "privacy") && (
            <Button
              onClick={() => {
                void handleSavePreferences().then(() => {
                  onClose();
                });
              }}
              className="flex-1"
            >
              Save Settings
            </Button>
          )}
          {activeTab === "instructions" && (
            <Button
              onClick={() => {
                void handleSaveInstructions().then(() => {
                  onClose();
                });
              }}
              disabled={getWordCount(userInstructions) > 500}
              className="flex-1"
            >
              Save Instructions
            </Button>
          )}
          {activeTab === "account" && (
            <Button
              onClick={() => {
                void handleSaveProfile().then(() => {
                  onClose();
                });
              }}
              className="flex-1"
            >
              Save Profile
            </Button>
          )}
        </div>
      </DialogContent>

      {/* Delete Conversations Confirmation Dialog */}
      <Dialog open={showDeleteConversationsDialog} onOpenChange={setShowDeleteConversationsDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete All Conversations</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p className="text-sm text-muted-foreground">
              This action cannot be undone. This will permanently delete all your conversations and messages.
            </p>
          </div>
          <div className="flex gap-3">
            <Button
              variant="outline"
              onClick={() => setShowDeleteConversationsDialog(false)}
              className="flex-1"
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteAllConversations}
              className="flex-1"
            >
              Delete All
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Delete Memories Confirmation Dialog */}
      <Dialog open={showDeleteMemoriesDialog} onOpenChange={setShowDeleteMemoriesDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete All Memories</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p className="text-sm text-muted-foreground">
              This action cannot be undone. This will permanently delete all AI memories.
            </p>
          </div>
          <div className="flex gap-3">
            <Button
              variant="outline"
              onClick={() => setShowDeleteMemoriesDialog(false)}
              className="flex-1"
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteAllMemories}
              className="flex-1"
            >
              Delete All
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </Dialog>
  );
}
