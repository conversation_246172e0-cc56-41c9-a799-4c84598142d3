import { useState, useEffect } from "react";
import { useMutation, useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Eye, EyeOff, Key, Trash2, Zap, Infinity as InfinityIcon, Settings, User } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";

interface UserPreferences {
  aiProvider: "openai" | "anthropic" | "google" | "openrouter" | "groq" | "deepseek" | "grok" | "cohere" | "mistral";
  model: string;
  temperature: number;
  maxTokens: number;
  enabledTools?: string[];
  favoriteModels?: Array<{
    provider: string;
    model: string;
  }>;
  hideUserInfo?: boolean;
}

interface SettingsModalProps {
  onClose: () => void;
  preferences: UserPreferences | null;
}

const PROVIDER_CONFIGS = {
  openai: {
    name: "OpenAI",
    models: [
      "gpt-4o-mini-2024-07-18",
      "chatgpt-4o-latest", 
      "o3-mini",
      "o4-mini",
      "gpt-4.1",
      "gpt-4.1-mini",
      "gpt-4.1-nano",
      "gpt-4o",
      "gpt-4o-mini",
      "gpt-4-turbo",
      "gpt-3.5-turbo"
    ],
    keyPlaceholder: "sk-...",
    description: "GPT models from OpenAI",
    hasBuiltIn: true,
  },
  google: {
    name: "Google AI",
    models: [
      "gemini-2.0-flash",
      "gemini-2.0-flash-lite", 
      "gemini-2.5-pro-preview-05-06",
      "gemini-2.5-flash-preview-05-20",
      "gemini-1.5-pro",
      "gemini-1.5-flash"
    ],
    keyPlaceholder: "AIza...",
    description: "Gemini models from Google",
    hasBuiltIn: true,
  },
  anthropic: {
    name: "Anthropic",
    models: [
      "claude-sonnet-4-20250514",
      "claude-opus-4-20250514",
      "claude-3-7-sonnet-latest",
      "claude-3-5-sonnet-latest",
      "claude-3-5-haiku-latest",
      "claude-3-5-sonnet-20241022",
      "claude-3-haiku-20240307",
      "claude-3-sonnet-20240229",
      "claude-3-opus-20240229"
    ],
    keyPlaceholder: "sk-ant-...",
    description: "Claude models from Anthropic",
    hasBuiltIn: true,
  },
  openrouter: {
    name: "OpenRouter",
    models: [
      "deepseek/deepseek-chat-v3-0324:free",
      "deepseek/deepseek-r1:free",
      "tngtech/deepseek-r1t-chimera:free",
      "deepseek/deepseek-prover-v2:free",
      "mistralai/devstral-small:free",
      "qwen/qwen2.5-vl-72b-instruct:free",
      "mistralai/mistral-small-3.1-24b-instruct:free",
      "google/gemma-3-27b-it:free",
      "rekaai/reka-flash-3:free",
      "google/gemini-2.5-pro-exp-03-25:free",
      "qwen/qwen3-235b-a22b:free",
      "qwen/qwen3-30b-a3b:free",
      "qwen/qwen3-32b:free",
      "nvidia/llama-3.1-nemotron-ultra-253b-v1:free",
      "anthropic/claude-3.5-sonnet",
      "openai/gpt-4o",
      "google/gemini-pro-1.5",
      "meta-llama/llama-3.1-405b-instruct",
      "mistralai/mixtral-8x7b-instruct",
      "cohere/command-r-plus",
    ],
    keyPlaceholder: "sk-or-...",
    description: "Access to multiple AI models",
    hasBuiltIn: true,
  },
  groq: {
    name: "Groq",
    models: [
      "deepseek-r1-distill-llama-70b",
      "deepseek-r1-distill-qwen-32b",
      "llama-3.3-70b-versatile",
      "llama-3.2-90b-vision-preview",
      "llama3-70b-8192",
      "qwen-qwq-32b",
      "meta-llama/llama-4-scout-17b-16e-instruct",
      "meta-llama/llama-4-maverick-17b-128e-instruct",
      "compound-beta",
      "compound-beta-mini",
      "llama-3.1-405b-reasoning",
      "llama-3.1-70b-versatile",
      "llama-3.1-8b-instant",
      "mixtral-8x7b-32768",
      "gemma2-9b-it",
    ],
    keyPlaceholder: "gsk_...",
    description: "Ultra-fast inference",
    hasBuiltIn: true,
  },
  deepseek: {
    name: "DeepSeek",
    models: ["deepseek-chat", "deepseek-coder"],
    keyPlaceholder: "sk-...",
    description: "Reasoning and coding models",
    hasBuiltIn: true,
  },
  grok: {
    name: "Grok (xAI)",
    models: ["grok-beta", "grok-vision-beta"],
    keyPlaceholder: "xai-...",
    description: "Elon's AI with real-time data",
    hasBuiltIn: true,
  },
  cohere: {
    name: "Cohere",
    models: ["command-r-plus", "command-r", "command"],
    keyPlaceholder: "co_...",
    description: "Enterprise-grade language models",
    hasBuiltIn: true,
  },
  mistral: {
    name: "Mistral AI",
    models: [
      "accounts/fireworks/models/mistral-small-24b-instruct-2501",
      "mistral-large-latest",
      "mistral-medium-latest",
      "mistral-small-latest", 
      "codestral-latest",
    ],
    keyPlaceholder: "...",
    description: "European AI models",
    hasBuiltIn: true,
  },
  tavily: {
    name: "Tavily Search",
    models: [],
    keyPlaceholder: "tvly-...",
    description: "Real-time web search API",
    hasBuiltIn: true,
  },
  openweather: {
    name: "OpenWeatherMap",
    models: [],
    keyPlaceholder: "...",
    description: "Weather data API",
    hasBuiltIn: true,
  },
};

export function SettingsModal({ onClose, preferences }: SettingsModalProps) {
  const [activeTab, setActiveTab] = useState<"preferences" | "privacy" | "apikeys" | "usage">("preferences");
  const [settings, setSettings] = useState<UserPreferences>({
    aiProvider: "openai",
    model: "gpt-4o-mini",
    temperature: 0.7,
    maxTokens: 1000,
    enabledTools: ["web_search", "calculator", "datetime"],
    favoriteModels: [],
    hideUserInfo: false,
  });
  const [apiKeys, setApiKeys] = useState<Record<string, string>>({});
  const [showKeys, setShowKeys] = useState<Record<string, boolean>>({});

  const updatePreferences = useMutation(api.preferences.update);
  const upsertApiKey = useMutation(api.apiKeys.upsert);
  const removeApiKey = useMutation(api.apiKeys.remove);
  const userApiKeys = useQuery(api.apiKeys.list) || [];
  const usage = useQuery(api.usage.get);
  const limits = useQuery(api.usage.getLimits);

  useEffect(() => {
    if (preferences) {
      // Only extract the valid preference fields, excluding Convex-generated fields
      setSettings({
        aiProvider: preferences.aiProvider,
        model: preferences.model,
        temperature: preferences.temperature,
        maxTokens: preferences.maxTokens,
        enabledTools: preferences.enabledTools,
        favoriteModels: preferences.favoriteModels,
        hideUserInfo: preferences.hideUserInfo ?? false,
      });
    }
  }, [preferences]);

  const handleSavePreferences = async () => {
    await updatePreferences(settings);
  };

  const handleSaveApiKey = async (provider: keyof typeof PROVIDER_CONFIGS) => {
    const key = apiKeys[provider];
    if (key?.trim()) {
      await upsertApiKey({ provider, apiKey: key.trim() });
      setApiKeys(prev => ({ ...prev, [provider]: "" }));
    }
  };

  const handleRemoveApiKey = async (provider: keyof typeof PROVIDER_CONFIGS) => {
    await removeApiKey({ provider });
  };

  const toggleShowKey = (provider: string) => {
    setShowKeys(prev => ({ ...prev, [provider]: !prev[provider] }));
  };

  const hasApiKey = (provider: string) => {
    return userApiKeys.some(key => key.provider === provider && key.hasKey);
  };

  const getUsagePercentage = (used: number, limit: number) => {
    return Math.min((used / limit) * 100, 100);
  };

  const getUsageColor = (percentage: number) => {
    if (percentage >= 90) return "bg-red-500";
    if (percentage >= 70) return "bg-yellow-500";
    return "bg-green-500";
  };

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden p-0">
        <DialogHeader className="px-6 py-4 border-b">
          <DialogTitle className="text-xl font-semibold">Settings</DialogTitle>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as "preferences" | "privacy" | "apikeys" | "usage")} className="flex-1 flex flex-col">
          <TabsList className="grid w-full grid-cols-4 mx-6 mt-4">
            <TabsTrigger value="preferences" className="flex items-center gap-2">
              <Settings size={16} />
              AI Preferences
            </TabsTrigger>
            <TabsTrigger value="privacy" className="flex items-center gap-2">
              <User size={16} />
              Privacy
            </TabsTrigger>
            <TabsTrigger value="apikeys" className="flex items-center gap-2">
              <Key size={16} />
              API Keys
            </TabsTrigger>
            <TabsTrigger value="usage" className="flex items-center gap-2">
              <Zap size={16} />
              Usage
            </TabsTrigger>
          </TabsList>

          <div className="overflow-y-auto max-h-[60vh] px-6 py-4">
            <TabsContent value="preferences" className="space-y-6 mt-0">
              <Card>
                <CardHeader>
                  <CardTitle>AI Model Configuration</CardTitle>
                  <CardDescription>
                    Configure your preferred AI provider and model settings
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* AI Provider */}
                  <div className="space-y-2">
                    <Label htmlFor="provider">AI Provider</Label>
                    <Select
                      value={settings.aiProvider}
                      onValueChange={(value) => setSettings(prev => ({
                        ...prev,
                        aiProvider: value as UserPreferences["aiProvider"],
                        model: PROVIDER_CONFIGS[value as keyof typeof PROVIDER_CONFIGS].models[0] ?? "",
                      }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select provider" />
                      </SelectTrigger>
                      <SelectContent>
                        {Object.entries(PROVIDER_CONFIGS)
                          .filter(([key]) => !["tavily", "openweather"].includes(key))
                          .map(([key, config]) => (
                            <SelectItem key={key} value={key}>
                              {config.name} - {config.description}
                            </SelectItem>
                          ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Model */}
                  {PROVIDER_CONFIGS[settings.aiProvider].models.length > 0 && (
                    <div className="space-y-2">
                      <Label htmlFor="model">Model</Label>
                      <Select
                        value={settings.model}
                        onValueChange={(value) => setSettings(prev => ({ ...prev, model: value }))}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select model" />
                        </SelectTrigger>
                        <SelectContent>
                          {PROVIDER_CONFIGS[settings.aiProvider].models.map(model => (
                            <SelectItem key={model} value={model}>{model}</SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  )}

                  {/* Temperature */}
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <Label>Temperature</Label>
                      <Badge variant="secondary">{settings.temperature}</Badge>
                    </div>
                    <Slider
                      value={[settings.temperature]}
                      onValueChange={([value]) => setSettings(prev => ({ ...prev, temperature: value }))}
                      max={2}
                      min={0}
                      step={0.1}
                      className="w-full"
                    />
                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>Focused</span>
                      <span>Creative</span>
                    </div>
                  </div>

                  {/* Max Tokens */}
                  <div className="space-y-2">
                    <Label htmlFor="maxTokens">Max Tokens</Label>
                    <Input
                      id="maxTokens"
                      type="number"
                      min="100"
                      max="4000"
                      step="100"
                      value={settings.maxTokens}
                      onChange={(e) => setSettings(prev => ({ ...prev, maxTokens: parseInt(e.target.value) }))}
                    />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Privacy Tab */}
            <TabsContent value="privacy" className="space-y-6 mt-0">
              <Card>
                <CardHeader>
                  <CardTitle>Privacy Settings</CardTitle>
                  <CardDescription>
                    Control how your personal information is displayed
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="hideUserInfo">Hide User Information</Label>
                      <p className="text-sm text-muted-foreground">
                        Blur your name and email in the sidebar for privacy
                      </p>
                    </div>
                    <Switch
                      id="hideUserInfo"
                      checked={settings.hideUserInfo}
                      onCheckedChange={(checked) => setSettings(prev => ({ ...prev, hideUserInfo: checked }))}
                    />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* API Keys Tab */}
            <TabsContent value="apikeys" className="space-y-6 mt-0">
              <Card>
                <CardHeader>
                  <CardTitle>API Keys</CardTitle>
                  <CardDescription>
                    Add your own API keys for unlimited usage without counting against limits
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="bg-gradient-to-r from-blue-50 to-green-50 border border-blue-200 rounded-lg p-4 mb-6">
                    <div className="flex items-start gap-3">
                      <Zap className="text-blue-600 mt-1" size={20} />
                      <div>
                        <h3 className="font-medium text-blue-900 mb-1">Built-in API Keys vs Your Own</h3>
                        <div className="text-sm text-blue-800 space-y-1">
                          <p><strong>Built-in Keys:</strong> Free usage with monthly limits (messages & searches count)</p>
                          <p><strong>Your Keys:</strong> <InfinityIcon className="inline w-4 h-4" /> Unlimited usage - no limits or counting!</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    {Object.entries(PROVIDER_CONFIGS).map(([provider, config]) => (
                      <Card key={provider}>
                        <CardContent className="p-4">
                          <div className="flex items-center justify-between mb-3">
                            <div>
                              <h3 className="font-medium">{config.name}</h3>
                              <p className="text-sm text-muted-foreground">{config.description}</p>
                            </div>
                            <div className="flex items-center gap-2">
                              {hasApiKey(provider) && (
                                <Badge variant="secondary" className="bg-green-100 text-green-800">
                                  <InfinityIcon size={12} className="mr-1" />
                                  Unlimited
                                </Badge>
                              )}
                              {config.hasBuiltIn && (
                                <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                                  <Zap size={12} className="mr-1" />
                                  Built-in
                                </Badge>
                              )}
                            </div>
                          </div>

                          <div className="flex gap-2">
                            <div className="flex-1 relative">
                              <Input
                                type={showKeys[provider] ? "text" : "password"}
                                value={apiKeys[provider] || ""}
                                onChange={(e) => setApiKeys(prev => ({ ...prev, [provider]: e.target.value }))}
                                placeholder={config.keyPlaceholder}
                                className="pr-10"
                              />
                              <Button
                                type="button"
                                variant="ghost"
                                size="icon"
                                onClick={() => toggleShowKey(provider)}
                                className="absolute right-1 top-1 h-8 w-8"
                              >
                                {showKeys[provider] ? <EyeOff size={16} /> : <Eye size={16} />}
                              </Button>
                            </div>
                            <Button
                              onClick={() => void handleSaveApiKey(provider as keyof typeof PROVIDER_CONFIGS)}
                              disabled={!apiKeys[provider]?.trim()}
                              className="flex items-center gap-2"
                            >
                              <Key size={16} />
                              Save
                            </Button>
                            {hasApiKey(provider) && (
                              <Button
                                variant="destructive"
                                onClick={() => void handleRemoveApiKey(provider as keyof typeof PROVIDER_CONFIGS)}
                                className="flex items-center gap-2"
                              >
                                <Trash2 size={16} />
                              </Button>
                            )}
                          </div>

                          {!hasApiKey(provider) && config.hasBuiltIn && (
                            <p className="text-xs text-muted-foreground mt-2">
                              Using built-in key with usage limits. Add your own key for unlimited access.
                            </p>
                          )}
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
            {/* Usage Tab */}
            <TabsContent value="usage" className="space-y-6 mt-0">
              <Card>
                <CardHeader>
                  <CardTitle>Current Plan</CardTitle>
                  <CardDescription>
                    Your usage statistics and plan information
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-6">
                    <h3 className="text-lg font-semibold mb-2">Current Plan</h3>
                    <div className="flex items-center gap-3">
                      <Badge className="capitalize">
                        {usage?.plan ?? "Free"}
                      </Badge>
                      <span className="text-muted-foreground">
                        Resets on {usage?.resetDate ? new Date(usage.resetDate).toLocaleDateString() : "N/A"}
                      </span>
                    </div>
                    <p className="text-sm text-blue-800 mt-2">
                      💡 Add your own API keys for unlimited usage without counting against these limits!
                    </p>
                  </div>
                </CardContent>
              </Card>

              {/* Usage Stats */}
              {usage && limits && (
                <Card>
                  <CardHeader>
                    <CardTitle>Usage Statistics</CardTitle>
                    <CardDescription>Built-in keys only - your own keys have unlimited usage</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {/* Messages Usage */}
                      <div className="space-y-3">
                        <h4 className="font-medium">Messages</h4>
                        <div className="space-y-2">
                          <div className="flex justify-between text-sm">
                            <span>Used</span>
                            <span>{usage.messagesUsed} / {limits.messages}</span>
                          </div>
                          <div className="w-full bg-muted rounded-full h-2">
                            <div
                              className={`h-2 rounded-full transition-all ${getUsageColor(getUsagePercentage(usage.messagesUsed, limits.messages))}`}
                              style={{ width: `${getUsagePercentage(usage.messagesUsed, limits.messages)}%` }}
                            />
                          </div>
                          <p className="text-xs text-muted-foreground">Your own API keys = unlimited messages</p>
                        </div>
                      </div>

                      {/* Searches Usage */}
                      <div className="space-y-3">
                        <h4 className="font-medium">Web Searches</h4>
                        <div className="space-y-2">
                          <div className="flex justify-between text-sm">
                            <span>Used</span>
                            <span>{usage.searchesUsed} / {limits.searches}</span>
                          </div>
                          <div className="w-full bg-muted rounded-full h-2">
                            <div
                              className={`h-2 rounded-full transition-all ${getUsageColor(getUsagePercentage(usage.searchesUsed, limits.searches))}`}
                              style={{ width: `${getUsagePercentage(usage.searchesUsed, limits.searches)}%` }}
                            />
                          </div>
                          <p className="text-xs text-muted-foreground">Your own API keys = unlimited searches</p>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

            </TabsContent>
          </div>
        </Tabs>

        <div className="flex gap-3 p-6 border-t">
          <Button variant="outline" onClick={onClose} className="flex-1">
            Cancel
          </Button>
          {(activeTab === "preferences" || activeTab === "privacy") && (
            <Button
              onClick={() => {
                void handleSavePreferences().then(() => {
                  onClose();
                });
              }}
              className="flex-1"
            >
              Save Settings
            </Button>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
